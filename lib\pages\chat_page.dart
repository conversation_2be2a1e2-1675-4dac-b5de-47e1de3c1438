import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/bot_model.dart';
import '../providers/history_provider.dart';
import '../controllers/chat_controller.dart';
import '../services/favorites_service.dart';
import '../widgets/profile_drawer.dart';
import '../widgets/profile_avatar_button.dart';
import '../widgets/chat/message_bubble.dart';
import '../widgets/chat/chat_message_input.dart';

class ChatPage extends StatefulWidget {
  final Bot bot;

  const ChatPage({super.key, required this.bot});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final HistoryProvider _historyProvider = Get.find<HistoryProvider>();
  late final ChatController _controller;

  @override
  void initState() {
    super.initState();
    // El FavoritesService ya debería estar registrado desde main.dart
    // pero verificamos por seguridad
    if (!Get.isRegistered<FavoritesService>()) {
      Get.put(FavoritesService(), permanent: true);
    }

    // Crear e inicializar el controlador
    _controller = Get.put(
      ChatController(bot: widget.bot),
      tag: widget.bot.id.toString(),
    );

    // Desplazarse al final cuando se carguen los mensajes inicialmente
    // Usar un delay para asegurar que el historial esté cargado
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 200), () {
        final messages = _historyProvider.currentHistory;
        if (messages.isNotEmpty) {
          _controller.scrollToBottom();
        }
      });
    });
  }

  @override
  void dispose() {
    // Limpiar el controlador específico de este bot
    Get.delete<ChatController>(tag: widget.bot.id.toString());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: theme.colorScheme.secondaryContainer,
              child: Icon(
                widget.bot.icon,
                color: theme.colorScheme.onSecondaryContainer,
              ),
            ),
            const SizedBox(width: 12),
            Text(widget.bot.name),
          ],
        ),
        elevation: 1,
        actions: const [ProfileAvatarButton()],
      ),
      endDrawer: const ProfileDrawer(),
      body: Column(
        children: [
          // Área de mensajes
          Expanded(
            child: Obx(() {
              final messages = _historyProvider.currentHistory;

              if (messages.isEmpty) {
                return Center(
                  child: Text(
                    'No hay mensajes. ¡Comienza la conversación!',
                    style: theme.textTheme.bodyLarge,
                  ),
                );
              }

              return ListView.builder(
                controller: _controller.scrollController,
                reverse: false,
                padding: const EdgeInsets.all(16),
                itemCount: messages.length,
                // Detectar gestos de deslizamiento para ocultar el teclado
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                itemBuilder: (context, index) {
                  final message = messages[index];
                  final isUserMessage =
                      message.usuario == message.usuarioDestino;

                  return MessageBubble(
                    message: message,
                    isUserMessage: isUserMessage,
                    botId: widget.bot.id.toString(),
                  );
                },
              );
            }),
          ),

          // Área de entrada de mensaje
          ChatMessageInput(botId: widget.bot.id.toString()),
        ],
      ),
    );
  }
}
