import 'package:flutter/foundation.dart';
import '../models/chart_models.dart';
import '../models/history_model.dart';
import '../utils/response_formatter.dart';

class ChartService {
  static const List<String> _colors = [
    '#FF6384',
    '#36A2EB',
    '#FFCE56',
    '#4BC0C0',
    '#9966FF',
    '#FF9F40',
    '#FF6384',
    '#C9CBCF',
    '#4BC0C0',
    '#FF6384',
  ];

  /// Procesa los datos de un mensaje TablaJSON para generar gráficas
  static ProcessedChartData processChartData({
    required ChatHistory message,
    String? selectedGroupBy,
    List<String>? selectedMetrics,
    String? selectedDateColumn,
    int? selectedYear,
    int? selectedMonth,
    // Parámetros específicos para mapa de calor
    String? selectedVerticalGroupBy,
    String? selectedHorizontalGroupBy,
    String? selectedHeatMetric,
  }) {
    try {
      // Obtener datos formateados del mensaje
      List<Map<String, dynamic>> data = ResponseFormatter.formatResponse(
        message,
      );

      if (data.isEmpty) {
        return ProcessedChartData.empty();
      }

      // Aplicar filtros de fecha si están especificados
      if (selectedDateColumn != null && selectedDateColumn.isNotEmpty) {
        data = _filterDataByDate(
          data,
          selectedDateColumn,
          selectedYear,
          selectedMonth,
        );
        if (data.isEmpty) {
          return ProcessedChartData.empty();
        }
      }

      // Si no se especifican parámetros, usar valores por defecto
      final columns = data.first.keys.toList();
      final groupBy = selectedGroupBy ?? _getDefaultGroupByColumn(columns);
      final metrics = selectedMetrics ?? _getDefaultMetrics(columns);

      if (groupBy.isEmpty || metrics.isEmpty) {
        return ProcessedChartData.empty();
      }

      // Procesar datos agrupados
      final groupedData = _processDataSet(data, groupBy, metrics);

      // Obtener etiquetas ordenadas
      final sortedLabels = _getSortedLabels(groupedData.keys.toList());

      // Crear datasets para cada métrica
      final datasets = _createDatasets(groupedData, sortedLabels, metrics);

      final chartData = ChartData(
        labels: sortedLabels,
        datasets: datasets,
        legend: datasets.map((d) => d.label).toList(),
      );

      // Procesar datos para mapa de calor
      final heatMapData = _processHeatMapData(
        data,
        selectedVerticalGroupBy ?? groupBy,
        selectedHorizontalGroupBy ?? groupBy,
        selectedHeatMetric ?? (metrics.isNotEmpty ? metrics.first : ''),
      );

      return ProcessedChartData(
        bar: chartData,
        line: chartData,
        heat: heatMapData,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error processing chart data: $e');
      }
      return ProcessedChartData.empty();
    }
  }

  /// Obtiene las columnas disponibles para agrupar
  static List<String> getAvailableGroupByColumns(ChatHistory message) {
    try {
      final List<Map<String, dynamic>> data = ResponseFormatter.formatResponse(
        message,
      );
      if (data.isEmpty) return [];

      return data.first.keys.toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting group by columns: $e');
      }
      return [];
    }
  }

  /// Obtiene las métricas numéricas disponibles
  static List<String> getAvailableMetrics(ChatHistory message) {
    try {
      final List<Map<String, dynamic>> data = ResponseFormatter.formatResponse(
        message,
      );
      if (data.isEmpty) return [];

      final firstRow = data.first;
      final numericColumns = <String>[];

      for (final entry in firstRow.entries) {
        if (_isNumericColumn(data, entry.key)) {
          numericColumns.add(entry.key);
        }
      }

      return numericColumns;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting available metrics: $e');
      }
      return [];
    }
  }

  /// Obtiene las columnas de fecha disponibles
  static List<String> getAvailableDateColumns(ChatHistory message) {
    try {
      final List<Map<String, dynamic>> data = ResponseFormatter.formatResponse(
        message,
      );
      if (data.isEmpty) return [];

      final firstRow = data.first;
      final dateColumns = <String>[];

      for (final entry in firstRow.entries) {
        if (_isDateColumn(data, entry.key)) {
          dateColumns.add(entry.key);
        }
      }

      return dateColumns;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting available date columns: $e');
      }
      return [];
    }
  }

  /// Obtiene los años disponibles de una columna de fecha
  static List<int> getAvailableYears(ChatHistory message, String dateColumn) {
    try {
      final List<Map<String, dynamic>> data = ResponseFormatter.formatResponse(
        message,
      );
      if (data.isEmpty) return [];

      final years = <int>{};
      for (final row in data) {
        final dateValue = _parseDate(row[dateColumn]);
        if (dateValue != null) {
          years.add(dateValue.year);
        }
      }

      final sortedYears = years.toList()..sort();
      return sortedYears;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting available years: $e');
      }
      return [];
    }
  }

  /// Obtiene los meses disponibles de una columna de fecha para un año específico
  static List<int> getAvailableMonths(
    ChatHistory message,
    String dateColumn,
    int year,
  ) {
    try {
      final List<Map<String, dynamic>> data = ResponseFormatter.formatResponse(
        message,
      );
      if (data.isEmpty) return [];

      final months = <int>{};
      for (final row in data) {
        final dateValue = _parseDate(row[dateColumn]);
        if (dateValue != null && dateValue.year == year) {
          months.add(dateValue.month);
        }
      }

      final sortedMonths = months.toList()..sort();
      return sortedMonths;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting available months: $e');
      }
      return [];
    }
  }

  /// Procesa un conjunto de datos agrupándolos
  static Map<String, Map<String, MetricData>> _processDataSet(
    List<Map<String, dynamic>> data,
    String groupBy,
    List<String> metrics,
  ) {
    final Map<String, Map<String, MetricData>> groupedData = {};

    for (final item in data) {
      final groupKey = item[groupBy]?.toString() ?? "Sin valor";

      if (!groupedData.containsKey(groupKey)) {
        groupedData[groupKey] = {};
        for (final metric in metrics) {
          groupedData[groupKey]![metric] = MetricData(total: 0, count: 0);
        }
      }

      for (final metric in metrics) {
        final value = _parseNumericValue(item[metric]);
        final current = groupedData[groupKey]![metric]!;
        groupedData[groupKey]![metric] = MetricData(
          total: current.total + value,
          count: current.count + 1,
        );
      }
    }

    return groupedData;
  }

  /// Crea datasets para las gráficas
  static List<ChartDataset> _createDatasets(
    Map<String, Map<String, MetricData>> groupedData,
    List<String> sortedLabels,
    List<String> metrics,
  ) {
    final datasets = <ChartDataset>[];

    for (int i = 0; i < metrics.length; i++) {
      final metric = metrics[i];
      final data = sortedLabels.map((label) {
        return groupedData[label]?[metric]?.total ?? 0.0;
      }).toList();

      datasets.add(
        ChartDataset(
          data: data,
          color: _colors[i % _colors.length],
          label: metric,
        ),
      );
    }

    return datasets;
  }

  /// Obtiene etiquetas ordenadas
  static List<String> _getSortedLabels(List<String> labels) {
    return labels..sort((a, b) {
      // Intentar ordenar numéricamente si es posible
      final numA = double.tryParse(a);
      final numB = double.tryParse(b);

      if (numA != null && numB != null) {
        return numA.compareTo(numB);
      }

      return a.compareTo(b);
    });
  }

  /// Obtiene la columna por defecto para agrupar
  static String _getDefaultGroupByColumn(List<String> columns) {
    // Buscar columnas comunes para agrupar
    final preferredColumns = [
      'fecha',
      'date',
      'categoria',
      'category',
      'tipo',
      'type',
    ];

    for (final preferred in preferredColumns) {
      final found = columns.firstWhere(
        (col) => col.toLowerCase().contains(preferred),
        orElse: () => '',
      );
      if (found.isNotEmpty) return found;
    }

    // Si no encuentra ninguna preferida, usar la primera columna
    return columns.isNotEmpty ? columns.first : '';
  }

  /// Obtiene las métricas por defecto
  static List<String> _getDefaultMetrics(List<String> columns) {
    final numericColumns = <String>[];

    // Buscar columnas que probablemente sean numéricas
    for (final column in columns) {
      final lowerColumn = column.toLowerCase();
      if (lowerColumn.contains('total') ||
          lowerColumn.contains('cantidad') ||
          lowerColumn.contains('count') ||
          lowerColumn.contains('valor') ||
          lowerColumn.contains('value') ||
          lowerColumn.contains('precio') ||
          lowerColumn.contains('price')) {
        numericColumns.add(column);
      }
    }

    // Si no encuentra columnas obvias, tomar las primeras 2 columnas
    if (numericColumns.isEmpty && columns.length > 1) {
      numericColumns.addAll(columns.take(2));
    }

    return numericColumns;
  }

  /// Verifica si una columna es numérica
  static bool _isNumericColumn(List<Map<String, dynamic>> data, String column) {
    int numericCount = 0;
    int totalCount = 0;

    for (final row in data.take(10)) {
      // Verificar solo las primeras 10 filas
      final value = row[column];
      if (value != null) {
        totalCount++;
        if (_parseNumericValue(value) != 0 || value.toString() == '0') {
          numericCount++;
        }
      }
    }

    // Considerar numérica si al menos 70% de los valores son números
    return totalCount > 0 && (numericCount / totalCount) >= 0.7;
  }

  /// Parsea un valor a número
  static double _parseNumericValue(dynamic value) {
    if (value == null) return 0.0;

    if (value is num) return value.toDouble();

    final stringValue = value.toString().trim();
    if (stringValue.isEmpty) return 0.0;

    // Remover caracteres no numéricos comunes
    final cleanValue = stringValue
        .replaceAll(',', '')
        .replaceAll('\$', '')
        .replaceAll('%', '');

    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Verifica si una columna es de fecha
  static bool _isDateColumn(List<Map<String, dynamic>> data, String column) {
    int dateCount = 0;
    int totalCount = 0;

    for (final row in data.take(10)) {
      // Verificar solo las primeras 10 filas
      final value = row[column];
      if (value != null) {
        totalCount++;
        if (_parseDate(value) != null) {
          dateCount++;
        }
      }
    }

    // Considerar fecha si al menos 70% de los valores son fechas válidas
    return totalCount > 0 && (dateCount / totalCount) >= 0.7;
  }

  /// Parsea un valor a fecha
  static DateTime? _parseDate(dynamic value) {
    if (value == null) return null;

    if (value is DateTime) return value;

    final stringValue = value.toString().trim();
    if (stringValue.isEmpty) return null;

    // Intentar diferentes formatos de fecha
    final dateFormats = [
      // ISO 8601
      RegExp(r'^\d{4}-\d{2}-\d{2}'),
      // DD/MM/YYYY o MM/DD/YYYY
      RegExp(r'^\d{1,2}/\d{1,2}/\d{4}'),
      // DD-MM-YYYY o MM-DD-YYYY
      RegExp(r'^\d{1,2}-\d{1,2}-\d{4}'),
      // YYYY/MM/DD
      RegExp(r'^\d{4}/\d{1,2}/\d{1,2}'),
    ];

    for (final format in dateFormats) {
      if (format.hasMatch(stringValue)) {
        try {
          return DateTime.parse(stringValue);
        } catch (e) {
          // Intentar con diferentes separadores
          try {
            final normalized = stringValue
                .replaceAll('/', '-')
                .replaceAll(' ', 'T');
            return DateTime.parse(normalized);
          } catch (e) {
            // Intentar formato DD/MM/YYYY
            final parts = stringValue.split(RegExp(r'[/-]'));
            if (parts.length >= 3) {
              try {
                final day = int.parse(parts[0]);
                final month = int.parse(parts[1]);
                final year = int.parse(parts[2]);
                return DateTime(year, month, day);
              } catch (e) {
                // Intentar formato MM/DD/YYYY
                try {
                  final month = int.parse(parts[0]);
                  final day = int.parse(parts[1]);
                  final year = int.parse(parts[2]);
                  return DateTime(year, month, day);
                } catch (e) {
                  continue;
                }
              }
            }
          }
        }
      }
    }

    return null;
  }

  /// Filtra los datos por fecha
  static List<Map<String, dynamic>> _filterDataByDate(
    List<Map<String, dynamic>> data,
    String dateColumn,
    int? selectedYear,
    int? selectedMonth,
  ) {
    return data.where((row) {
      final dateValue = _parseDate(row[dateColumn]);
      if (dateValue == null) return false;

      // Filtrar por año si está especificado
      if (selectedYear != null && dateValue.year != selectedYear) {
        return false;
      }

      // Filtrar por mes si está especificado
      if (selectedMonth != null && dateValue.month != selectedMonth) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Procesa los datos para crear un mapa de calor
  static HeatMapData? _processHeatMapData(
    List<Map<String, dynamic>> data,
    String verticalGroupBy,
    String horizontalGroupBy,
    String metric,
  ) {
    try {
      if (data.isEmpty || metric.isEmpty) return null;

      // Validar que los agrupadores no sean iguales
      if (verticalGroupBy == horizontalGroupBy) return null;

      // Obtener valores únicos para cada agrupador
      final verticalLabels = <String>[];
      final horizontalLabels = <String>[];

      for (final item in data) {
        final verticalValue = item[verticalGroupBy]?.toString() ?? "Sin valor";
        final horizontalValue =
            item[horizontalGroupBy]?.toString() ?? "Sin valor";

        if (!verticalLabels.contains(verticalValue)) {
          verticalLabels.add(verticalValue);
        }
        if (!horizontalLabels.contains(horizontalValue)) {
          horizontalLabels.add(horizontalValue);
        }
      }

      // Ordenar etiquetas
      verticalLabels.sort((a, b) {
        final numA = double.tryParse(a);
        final numB = double.tryParse(b);
        if (numA != null && numB != null) {
          return numA.compareTo(numB);
        }
        return a.compareTo(b);
      });

      horizontalLabels.sort((a, b) {
        final numA = double.tryParse(a);
        final numB = double.tryParse(b);
        if (numA != null && numB != null) {
          return numA.compareTo(numB);
        }
        return a.compareTo(b);
      });

      // Crear matriz de datos
      final heatMapMatrix = <List<double>>[];

      for (final verticalLabel in verticalLabels) {
        final row = <double>[];

        for (final horizontalLabel in horizontalLabels) {
          // Buscar todos los registros que coincidan con esta combinación
          double totalValue = 0.0;

          for (final item in data) {
            final itemVertical =
                item[verticalGroupBy]?.toString() ?? "Sin valor";
            final itemHorizontal =
                item[horizontalGroupBy]?.toString() ?? "Sin valor";

            if (itemVertical == verticalLabel &&
                itemHorizontal == horizontalLabel) {
              final value = _parseNumericValue(item[metric]);
              totalValue += value;
            }
          }

          row.add(totalValue);
        }
        heatMapMatrix.add(row);
      }

      return HeatMapData(
        verticalLabels: verticalLabels,
        horizontalLabels: horizontalLabels,
        data: heatMapMatrix,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error processing heat map data: $e');
      }
      return null;
    }
  }

  /// Combina datos principales con datos de comparación
  static ChartData combineWithComparisonData(
    ChartData mainData,
    ChartData comparisonData,
    int comparisonYear,
  ) {
    if (mainData.isEmpty || comparisonData.isEmpty) {
      return mainData;
    }

    // Crear nuevos datasets combinados
    final combinedDatasets = <ChartDataset>[];

    // Agregar datasets principales
    combinedDatasets.addAll(mainData.datasets);

    // Agregar datasets de comparación con colores diferentes y sufijo en el label
    for (int i = 0; i < comparisonData.datasets.length; i++) {
      final comparisonDataset = comparisonData.datasets[i];

      // Usar colores más oscuros/diferentes para la comparación
      final comparisonColorIndex =
          (i + mainData.datasets.length) % _colors.length;
      final comparisonColor = _getComparisonColor(
        _colors[comparisonColorIndex],
      );

      combinedDatasets.add(
        ChartDataset(
          data: comparisonDataset.data,
          color: comparisonColor,
          label: '${comparisonDataset.label} ($comparisonYear)',
          strokeWidth: comparisonDataset.strokeWidth,
        ),
      );
    }

    // Crear leyenda combinada
    final combinedLegend = combinedDatasets.map((d) => d.label).toList();

    return ChartData(
      labels: mainData.labels,
      datasets: combinedDatasets,
      legend: combinedLegend,
    );
  }

  /// Genera un color más oscuro para la comparación
  static String _getComparisonColor(String originalColor) {
    // Convertir el color hex a más oscuro agregando transparencia o modificando el tono
    final colorMap = {
      '#FF6384': '#CC4F6B', // Rojo más oscuro
      '#36A2EB': '#2B82BC', // Azul más oscuro
      '#FFCE56': '#CCA545', // Amarillo más oscuro
      '#4BC0C0': '#3C9A9A', // Cian más oscuro
      '#9966FF': '#7A52CC', // Púrpura más oscuro
      '#FF9F40': '#CC7F33', // Naranja más oscuro
    };

    return colorMap[originalColor] ?? originalColor;
  }
}
