import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/bot_model.dart';
import '../providers/history_provider.dart';
import '../services/favorites_service.dart';
import '../services/chat_service.dart';

class ChatController extends GetxController {
  final Bot bot;
  final HistoryProvider _historyProvider = Get.find<HistoryProvider>();
  final FavoritesService _favoritesService = Get.find<FavoritesService>();
  final ChatService _chatService = Get.find<ChatService>();

  final TextEditingController messageController = TextEditingController();
  final ScrollController scrollController = ScrollController();
  final RxBool isSending = false.obs;

  ChatController({required this.bot});

  @override
  void onInit() {
    super.onInit();
    // Asegurarse de que el bot actual esté configurado correctamente
    _historyProvider.currentBotId = bot.id.toString();

    // Desplazar al final cuando se carguen los mensajes inicialmente
    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollToBottom();
    });
  }

  @override
  void onClose() {
    messageController.dispose();
    scrollController.dispose();
    super.onClose();
  }

  Future<void> sendMessage({String? message}) async {
    final textToSend = message ?? messageController.text.trim();
    if (textToSend.isEmpty) return;

    isSending.value = true;
    if (message == null) messageController.clear();

    try {
      // Usar el ChatService para enviar el mensaje
      await _chatService.sendMessage(bot: bot, message: textToSend);

      // Desplazar al final de la lista después del envío exitoso
      scrollToBottom();
    } catch (e) {
      // El error se manejará en la UI
      rethrow;
    } finally {
      isSending.value = false;
    }
  }

  bool isFavorite(int messageRid) {
    return _favoritesService.isFavorite(messageRid);
  }

  Future<void> toggleFavorite(message, BuildContext context) async {
    await _favoritesService.toggleFavorite(message, context);
  }

  void editMessage(String messageText) {
    messageController.text = messageText;
    messageController.selection = TextSelection.fromPosition(
      TextPosition(offset: messageController.text.length),
    );
  }

  /// Desplaza la lista de mensajes al final (mensajes más recientes)
  void scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
}
